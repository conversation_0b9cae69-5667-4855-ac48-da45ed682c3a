package com.kc.web.controller.system;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.ProjectSalaryDetail;
import com.kc.system.service.IProjectSalaryDetailService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;

/**
 * 项目劳务费构成Controller
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@RestController
@RequestMapping("/system/detail")
public class ProjectSalaryDetailController extends BaseController
{
    @Autowired
    private IProjectSalaryDetailService projectSalaryDetailService;

    /**
     * 查询项目劳务费构成列表
     */
    @PreAuthorize("@ss.hasPermi('system:detail:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProjectSalaryDetail projectSalaryDetail)
    {
        startPage();
        List<ProjectSalaryDetail> list = projectSalaryDetailService.selectProjectSalaryDetailList(projectSalaryDetail);
        return getDataTable(list);
    }

    /**
     * 导出项目劳务费构成列表
     */
    @PreAuthorize("@ss.hasPermi('system:detail:export')")
    @Log(title = "项目劳务费构成", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectSalaryDetail projectSalaryDetail)
    {
        List<ProjectSalaryDetail> list = projectSalaryDetailService.selectProjectSalaryDetailList(projectSalaryDetail);
        ExcelUtil<ProjectSalaryDetail> util = new ExcelUtil<ProjectSalaryDetail>(ProjectSalaryDetail.class);
        util.exportExcel(response, list, "项目劳务费构成数据");
    }

    /**
     * 获取项目劳务费构成详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:detail:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(projectSalaryDetailService.selectProjectSalaryDetailById(id));
    }

    /**
     * 新增项目劳务费构成
     */
    @PreAuthorize("@ss.hasPermi('system:detail:add')")
    @Log(title = "项目劳务费构成", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProjectSalaryDetail projectSalaryDetail)
    {
        return toAjax(projectSalaryDetailService.insertProjectSalaryDetail(projectSalaryDetail));
    }

    /**
     * 修改项目劳务费构成
     */
    @PreAuthorize("@ss.hasPermi('system:detail:edit')")
    @Log(title = "项目劳务费构成", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectSalaryDetail projectSalaryDetail)
    {
        return toAjax(projectSalaryDetailService.updateProjectSalaryDetail(projectSalaryDetail));
    }

    /**
     * 删除项目劳务费构成
     */
    @PreAuthorize("@ss.hasPermi('system:detail:remove')")
    @Log(title = "项目劳务费构成", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(projectSalaryDetailService.deleteProjectSalaryDetailByIds(ids));
    }

    /**
     * 根据项目ID和工作月份查询劳务费明细
     */
    @PreAuthorize("@ss.hasPermi('system:detail:query')")
    @GetMapping("/project/{projectId}/{workMonth}")
    public TableDataInfo getProjectSalaryDetails(
        @PathVariable("projectId") Long projectId,
        @PathVariable("workMonth") String workMonth
    ) {
        startPage();
        List<Map<String, Object>> list = projectSalaryDetailService.selectProjectSalaryDetailsByProjectIdAndMonth(projectId, workMonth);
        return getDataTable(list);
    }
}
