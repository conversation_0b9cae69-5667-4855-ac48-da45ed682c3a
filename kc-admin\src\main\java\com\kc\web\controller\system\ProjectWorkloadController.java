package com.kc.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.Calendar;
import java.text.SimpleDateFormat;
import javax.servlet.http.HttpServletResponse;

import com.kc.system.service.ISysConfigService;
import com.kc.system.service.IEvaluationConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.ProjectWorkload;
import com.kc.system.service.IProjectWorkloadService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 项目工时记录Controller
 * 
 * <AUTHOR>
 * @date 2025-02-15
 */
@RestController
@RequestMapping("/system/workload")
public class ProjectWorkloadController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ProjectWorkloadController.class);

    @Autowired
    private IProjectWorkloadService projectWorkloadService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IEvaluationConfigService evaluationConfigService;
    /**
     * 查询项目工时记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:workload:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProjectWorkload projectWorkload)
    {
        startPage();
        List<ProjectWorkload> list = projectWorkloadService.selectProjectWorkloadList(projectWorkload);
        return getDataTable(list);
    }

    /**
     * 导出项目工时记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:workload:export')")
    @Log(title = "项目工时记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectWorkload projectWorkload)
    {
        List<ProjectWorkload> list = projectWorkloadService.selectProjectWorkloadList(projectWorkload);
        ExcelUtil<ProjectWorkload> util = new ExcelUtil<ProjectWorkload>(ProjectWorkload.class);
        util.exportExcel(response, list, "项目工时记录数据");
    }

    /**
     * 获取项目工时记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:workload:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(projectWorkloadService.selectProjectWorkloadById(id));
    }

    /**
     * 检查工时填报权限（使用精力分配的填报周期验证）
     */
    private void checkWorkloadPermission() {
        // 使用精力分配的填报周期验证，而不是工时填报周期验证
        if (!evaluationConfigService.checkOperationAllowed("participation", "common")) {
            throw new RuntimeException("当前不在精力分配填报周期内，禁止操作工时记录");
        }
    }

    /**
     * 新增项目工时记录
     */
    @PreAuthorize("@ss.hasPermi('system:workload:add')")
    @Log(title = "项目工时记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProjectWorkload projectWorkload)
    {
        // 检查工时填报权限
        checkWorkloadPermission();

        // 将工作量归集的月份+1，解决填报周期不匹配问题
        String adjustedMonth = adjustMonthForEvaluation(projectWorkload.getWorkMonth());
        projectWorkload.setWorkMonth(adjustedMonth);

        int result = projectWorkloadService.insertProjectWorkload(projectWorkload);
        if (result > 0) {
            return AjaxResult.success("工作量归集数据已保存（新增或更新）");
        } else {
            return AjaxResult.error("保存失败");
        }
    }

    /**
     * 修改项目工时记录
     */
    @PreAuthorize("@ss.hasPermi('system:workload:edit')")
    @Log(title = "项目工时记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectWorkload projectWorkload)
    {
        // 检查工时填报权限
        checkWorkloadPermission();

        // 将工作量归集的月份+1，解决填报周期不匹配问题
        String adjustedMonth = adjustMonthForEvaluation(projectWorkload.getWorkMonth());
        projectWorkload.setWorkMonth(adjustedMonth);

        return toAjax(projectWorkloadService.updateProjectWorkload(projectWorkload));
    }

    /**
     * 删除项目工时记录
     */
    @PreAuthorize("@ss.hasPermi('system:workload:remove')")
    @Log(title = "项目工时记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        // 检查工时填报权限
        checkWorkloadPermission();
        return toAjax(projectWorkloadService.deleteProjectWorkloadByIds(ids));
    }

    /**
     * 获取项目统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:workload:list')")
    @GetMapping("/stats/{projectId}")
    public AjaxResult getProjectStats(
        @PathVariable("projectId") Long projectId,
        @RequestParam(required = false) String workMonth
    ) {
        return success(projectWorkloadService.getProjectStats(projectId, workMonth));
    }

    /**
     * 获取所有项目统计数据（不分页）
     */
    @PreAuthorize("@ss.hasPermi('system:workload:list')")
    @GetMapping("/stats/all")
    public AjaxResult getAllProjectStats(@RequestParam(required = false) String workMonth) {
        List<Map<String, Object>> stats = projectWorkloadService.getAllProjectStats(workMonth);
        return success(stats);
    }

    /**
     * 统计当前月份中未填报工时的人数及项目名称
     */
    @PreAuthorize("@ss.hasPermi('system:workload:list')")
    @GetMapping("/unfilled")
    public AjaxResult getUnfilledWorkloadCount(@RequestParam String workMonth) {
        List<Map<String, Object>> result = projectWorkloadService.selectUnfilledWorkloadCount(workMonth);
        return success(result);
    }

    /**
     * 获取用户当月工时记录
     */
    @PreAuthorize("@ss.hasPermi('system:workload:list')")
    @GetMapping("/userMonthly")
    public AjaxResult getUserMonthlyWorkload(@RequestParam String userName, @RequestParam String workMonth) {
        ProjectWorkload query = new ProjectWorkload();
        query.setUserName(userName);
        query.setWorkMonth(workMonth);

        // 使用专门的方法获取用户实际工时记录
        List<ProjectWorkload> actualWorkloads = projectWorkloadService.selectUserActualWorkload(query);

        return success(actualWorkloads);
    }

    /**
     * 调整工作量归集月份，解决填报周期不匹配问题
     * 将填报月份+1，使得评分时能查询到对应的工作量归集数据
     *
     * @param originalMonth 原始填报月份 (格式: yyyy-MM)
     * @return 调整后的月份 (格式: yyyy-MM)
     */
    private String adjustMonthForEvaluation(String originalMonth) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Calendar cal = Calendar.getInstance();
            cal.setTime(sdf.parse(originalMonth));
            cal.add(Calendar.MONTH, 1);  // 月份+1
            String adjustedMonth = sdf.format(cal.getTime());

            // 记录调整信息
            logger.info("工作量归集月份调整: {} -> {}", originalMonth, adjustedMonth);
            return adjustedMonth;
        } catch (Exception e) {
            logger.error("月份调整失败，使用原月份: {}", originalMonth, e);
            return originalMonth;
        }
    }

}
