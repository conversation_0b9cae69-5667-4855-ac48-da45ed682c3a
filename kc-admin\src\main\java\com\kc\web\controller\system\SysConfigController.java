package com.kc.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.core.page.TableDataInfo;
import com.kc.common.enums.BusinessType;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.system.domain.SysConfig;
import com.kc.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 参数配置 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/config")
public class SysConfigController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(SysConfigController.class);

    @Autowired
    private ISysConfigService configService;

    /**
     * 获取参数配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysConfig config)
    {
        startPage();
        List<SysConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    @Log(title = "参数管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:config:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysConfig config)
    {
        List<SysConfig> list = configService.selectConfigList(config);
        ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
        util.exportExcel(response, list, "参数数据");
    }

    /**
     * 根据参数编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable Long configId)
    {
        return success(configService.selectConfigById(configId));
    }

    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/configKey/{configKey}")
    public AjaxResult getConfigKey(@PathVariable String configKey)
    {
        String configValue = configService.selectConfigByKey(configKey);
        log.info("Config key: {}, value: {}", configKey, configValue);
        return success(configValue);
    }

    /**
     * 新增参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "参数管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysConfig config)
    {
        if (!configService.checkConfigKeyUnique(config))
        {
            return error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setCreateBy(getUsername());
        return toAjax(configService.insertConfig(config));
    }

    /**
     * 修改参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysConfig config)
    {
        if (!configService.checkConfigKeyUnique(config))
        {
            return error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
        }
        config.setUpdateBy(getUsername());
        return toAjax(configService.updateConfig(config));
    }

    /**
     * 删除参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        configService.deleteConfigByIds(configIds);
        return success();
    }

    /**
     * 刷新参数缓存
     */
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "参数管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        configService.resetConfigCache();
        return success();
    }

    /**
     * 根据参数键名修改参数键值
     */
    @PreAuthorize("@ss.hasPermi('system:config:isputworkload')")
    @Log(title = "参数管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateByKey/{configKey}")
    public AjaxResult updateByKey(@PathVariable String configKey, @RequestBody String configValue)
    {
        log.info("收到更新配置请求 - configKey: {}, configValue: {}", configKey, configValue);

        // 获取现有配置
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        List<SysConfig> configs = configService.selectConfigList(config);

        if (configs == null || configs.isEmpty()) {
            log.error("配置不存在 - configKey: {}", configKey);
            return error("修改失败，参数键名'" + configKey + "'不存在");
        }

        // 更新配置
        SysConfig updateConfig = configs.get(0);
        log.info("更新前配置值: {}", updateConfig.getConfigValue());
        updateConfig.setConfigValue(configValue);
        updateConfig.setUpdateBy(getUsername());

        int result = configService.updateConfig(updateConfig);
        log.info("配置更新结果: {}, 更新后配置值: {}", result, configValue);

        // 验证更新是否成功
        List<SysConfig> verifyConfigs = configService.selectConfigList(config);
        if (!verifyConfigs.isEmpty()) {
            log.info("验证更新后的配置值: {}", verifyConfigs.get(0).getConfigValue());
        }

        return toAjax(result);
    }

    /**
     * 直接从数据库获取参数值
     */
    @GetMapping(value = "/configKeyFromDb/{configKey}")
    public AjaxResult getConfigKeyFromDb(@PathVariable String configKey)
    {
        // 创建查询条件
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        List<SysConfig> configs = configService.selectConfigList(config);
        
        if (configs != null && !configs.isEmpty()) {
            String configValue = configs.get(0).getConfigValue();
            log.info("Config from DB - key: {}, value: {}", configKey, configValue);
            return success(configValue);
        }
        return error("未找到配置项");
    }

    /**
     * 修复评分配置项
     */
    @PostMapping("/fixEvaluationConfig")
    public AjaxResult fixEvaluationConfig()
    {
        try {
            // 查找现有配置
            SysConfig queryConfig = new SysConfig();
            queryConfig.setConfigKey("evaluation.enabled");
            List<SysConfig> configs = configService.selectConfigList(queryConfig);

            if (configs != null && !configs.isEmpty()) {
                // 更新现有配置
                SysConfig config = configs.get(0);
                config.setConfigType("Y");
                config.setConfigValue("true");
                config.setUpdateBy(getUsername());
                configService.updateConfig(config);
                log.info("更新评分配置成功");
            } else {
                // 创建新配置
                SysConfig config = new SysConfig();
                config.setConfigName("评分功能开关");
                config.setConfigKey("evaluation.enabled");
                config.setConfigValue("true");
                config.setConfigType("Y");
                config.setCreateBy(getUsername());
                config.setRemark("控制整个考核评分功能的开启和关闭，true为开启，false为关闭");
                configService.insertConfig(config);
                log.info("创建评分配置成功");
            }

            // 重新加载缓存
            configService.resetConfigCache();

            return success("评分配置修复成功");
        } catch (Exception e) {
            log.error("修复评分配置失败", e);
            return error("修复评分配置失败: " + e.getMessage());
        }
    }
}
