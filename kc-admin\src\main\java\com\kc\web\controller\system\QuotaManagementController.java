package com.kc.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;
import com.kc.system.domain.QuotaManagement;
import com.kc.system.service.IQuotaManagementService;
import com.kc.system.service.IQuotaGroupService;

/**
 * 配额管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@RestController
@RequestMapping("/system/quotaManagement")
public class QuotaManagementController extends BaseController
{
    @Autowired
    private IQuotaManagementService quotaManagementService;

    @Autowired
    private IQuotaGroupService quotaGroupService;

    /**
     * 查询配额管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:list')")
    @GetMapping("/list")
    public TableDataInfo list(QuotaManagement quotaManagement)
    {
        startPage();
        List<QuotaManagement> list = quotaManagementService.selectQuotaManagementList(quotaManagement);
        return getDataTable(list);
    }

    /**
     * 导出配额管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:export')")
    @Log(title = "配额管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QuotaManagement quotaManagement)
    {
        List<QuotaManagement> list = quotaManagementService.selectQuotaManagementList(quotaManagement);
        ExcelUtil<QuotaManagement> util = new ExcelUtil<QuotaManagement>(QuotaManagement.class);
        util.exportExcel(response, list, "配额管理数据");
    }

    /**
     * 获取配额管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(quotaManagementService.selectQuotaManagementById(id));
    }

    /**
     * 新增配额管理
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:add')")
    @Log(title = "配额管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QuotaManagement quotaManagement)
    {
        return toAjax(quotaManagementService.insertQuotaManagement(quotaManagement));
    }

    /**
     * 修改配额管理
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:edit')")
    @Log(title = "配额管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuotaManagement quotaManagement)
    {
        return toAjax(quotaManagementService.updateQuotaManagement(quotaManagement));
    }

    /**
     * 删除配额管理
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:remove')")
    @Log(title = "配额管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(quotaManagementService.deleteQuotaManagementByIds(ids));
    }

    /**
     * 根据部门ID和年度查询配额
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:query')")
    @GetMapping("/dept/{deptId}/{year}")
    public AjaxResult getQuotaByDept(@PathVariable("deptId") Long deptId, @PathVariable("year") String year)
    {
        QuotaManagement quota = quotaManagementService.getQuotaByDeptAndYear(deptId, year);
        return success(quota);
    }

    /**
     * 检查配额是否可用
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:query')")
    @GetMapping("/check/{deptId}/{year}/{count}")
    public AjaxResult checkQuota(@PathVariable("deptId") Long deptId, 
                                @PathVariable("year") String year, 
                                @PathVariable("count") Integer count)
    {
        boolean available = quotaManagementService.checkQuotaAvailable(deptId, year, count);
        return success(available);
    }

    /**
     * 批量设置配额
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:batch')")
    @Log(title = "批量设置配额", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSet")
    public AjaxResult batchSetQuota(@RequestBody List<QuotaManagement> quotaManagementList)
    {
        return toAjax(quotaManagementService.batchSetQuota(quotaManagementList));
    }

    /**
     * 重置年度配额使用情况
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:reset')")
    @Log(title = "重置年度配额", businessType = BusinessType.UPDATE)
    @PostMapping("/reset/{year}")
    public AjaxResult resetYearQuota(@PathVariable("year") String year)
    {
        return toAjax(quotaManagementService.resetYearQuotaUsage(year));
    }

    /**
     * 查询配额统计
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:statistics')")
    @GetMapping("/statistics/{year}")
    public AjaxResult getStatistics(@PathVariable("year") String year)
    {
        List<QuotaManagement> statistics = quotaManagementService.getQuotaStatistics(year);
        return success(statistics);
    }

    /**
     * 初始化年度配额
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:edit')")
    @Log(title = "初始化年度配额", businessType = BusinessType.INSERT)
    @PostMapping("/init/{year}")
    public AjaxResult initYearQuota(@PathVariable("year") String year)
    {
        int result = quotaManagementService.initYearQuota(year);
        return success("成功初始化 " + result + " 个部门的配额");
    }

    /**
     * 更新部门人数
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:edit')")
    @Log(title = "更新部门人数", businessType = BusinessType.UPDATE)
    @PostMapping("/updateEmployeeCount/{deptId}/{year}")
    public AjaxResult updateEmployeeCount(@PathVariable("deptId") Long deptId, @PathVariable("year") String year)
    {
        return toAjax(quotaManagementService.updateDeptEmployeeCount(deptId, year));
    }

    /**
     * 批量更新部门人数
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:edit')")
    @Log(title = "批量更新部门人数", businessType = BusinessType.UPDATE)
    @PostMapping("/batchUpdateEmployeeCount/{year}")
    public AjaxResult batchUpdateEmployeeCount(@PathVariable("year") String year)
    {
        int result = quotaManagementService.batchUpdateDeptEmployeeCount(year);
        return success("成功更新 " + result + " 个部门的人数");
    }

    /**
     * 获取配额详情
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:query')")
    @GetMapping("/detail/{deptId}/{year}")
    public AjaxResult getQuotaDetail(@PathVariable("deptId") Long deptId, @PathVariable("year") String year)
    {
        QuotaManagement quota = quotaManagementService.getQuotaDetail(deptId, year);
        return success(quota);
    }

    /**
     * 检查部门是否属于配额组
     */
    @PreAuthorize("@ss.hasPermi('system:quotaManagement:query')")
    @GetMapping("/checkQuotaGroup/{deptId}")
    public AjaxResult checkDeptInQuotaGroup(@PathVariable("deptId") Long deptId)
    {
        boolean isInGroup = quotaGroupService.isDeptInQuotaGroup(deptId);
        return success(isInGroup);
    }
}
