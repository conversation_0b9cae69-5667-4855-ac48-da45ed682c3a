package com.kc.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.Calendar;
import java.text.SimpleDateFormat;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.ProjectParticipation;
import com.kc.system.service.IProjectParticipationService;
import com.kc.system.service.IEvaluationConfigService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;
import java.util.Calendar;
import com.kc.common.exception.ServiceException;

/**
 * 项目参与度分配Controller
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/system/participation")
public class ProjectParticipationController extends BaseController
{
    @Autowired
    private IProjectParticipationService projectParticipationService;

    @Autowired
    private IEvaluationConfigService evaluationConfigService;

    /**
     * 查询项目参与度分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:participation:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProjectParticipation projectParticipation)
    {
        startPage();
        List<ProjectParticipation> list = projectParticipationService.selectProjectParticipationList(projectParticipation);
        return getDataTable(list);
    }

    /**
     * 查询项目参与度分配列表（不分页）
     */
    @PreAuthorize("@ss.hasPermi('system:participation:list')")
    @GetMapping("/listAll")
    public AjaxResult listAll(ProjectParticipation projectParticipation)
    {
        List<ProjectParticipation> list = projectParticipationService.selectProjectParticipationList(projectParticipation);
        return success(list);
    }

    /**
     * 查询项目参与度分配列表（关联用户和部门信息，不分页）
     */
    @PreAuthorize("@ss.hasPermi('system:participation:list')")
    @GetMapping("/listAllWithUserInfo")
    public AjaxResult listAllWithUserInfo(ProjectParticipation projectParticipation)
    {
        List<Map<String, Object>> list = projectParticipationService.selectProjectParticipationWithUserInfo(projectParticipation);
        return success(list);
    }

    /**
     * 导出项目参与度分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:participation:export')")
    @Log(title = "项目参与度分配", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectParticipation projectParticipation)
    {
        List<ProjectParticipation> list = projectParticipationService.selectProjectParticipationList(projectParticipation);
        ExcelUtil<ProjectParticipation> util = new ExcelUtil<ProjectParticipation>(ProjectParticipation.class);
        util.exportExcel(response, list, "项目参与度分配数据");
    }

    /**
     * 获取项目参与度分配详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:participation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(projectParticipationService.selectProjectParticipationById(id));
    }

    /**
     * 新增项目参与度分配
     */
    @PreAuthorize("@ss.hasPermi('system:participation:add')")
    @Log(title = "项目参与度分配", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProjectParticipation projectParticipation)
    {
        // 检查是否在允许的填报周期内
        checkOperationAllowed(projectParticipation.getMonth());

        // 将精力分配的月份+1，解决填报周期不匹配问题
        String adjustedMonth = adjustMonthForEvaluation(projectParticipation.getMonth());
        projectParticipation.setMonth(adjustedMonth);

        return toAjax(projectParticipationService.insertProjectParticipation(projectParticipation));
    }

    /**
     * 修改项目参与度分配
     */
    @PreAuthorize("@ss.hasPermi('system:participation:edit')")
    @Log(title = "项目参与度分配", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectParticipation projectParticipation)
    {
        // 检查是否在允许的填报周期内
        checkOperationAllowed(projectParticipation.getMonth());

        // 将精力分配的月份+1，解决填报周期不匹配问题
        String adjustedMonth = adjustMonthForEvaluation(projectParticipation.getMonth());
        projectParticipation.setMonth(adjustedMonth);

        return toAjax(projectParticipationService.updateProjectParticipation(projectParticipation));
    }

    /**
     * 删除项目参与度分配
     */
    @PreAuthorize("@ss.hasPermi('system:participation:remove')")
    @Log(title = "项目参与度分配", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(projectParticipationService.deleteProjectParticipationByIds(ids));
    }
    
    /**
     * 批量提交项目参与度分配
     */
    @PreAuthorize("@ss.hasPermi('system:participation:add')")
    @Log(title = "批量提交项目参与度", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    public AjaxResult batchAdd(@RequestBody List<ProjectParticipation> participationList)
    {
        // 如果列表为空，直接返回
        if (participationList == null || participationList.isEmpty()) {
            return AjaxResult.error("提交的数据为空");
        }
        
        // 检查是否在允许的填报周期内
        // 只需要检查第一条记录的月份，假设所有记录都是同一个月份
        checkOperationAllowed(participationList.get(0).getMonth());

        int successCount = 0;
        int skippedCount = 0;

        for (ProjectParticipation participation : participationList) {
            // 将精力分配的月份+1，解决填报周期不匹配问题
            // 例如：11月30号前填报的精力分配，存储为12月数据，这样12月10号评分时能查到
            String adjustedMonth = adjustMonthForEvaluation(participation.getMonth());
            participation.setMonth(adjustedMonth);

            int result = projectParticipationService.insertProjectParticipation(participation);
            if (result > 0) {
                successCount++;
            } else {
                skippedCount++;
            }
        }

        String message = "成功处理 " + successCount + " 条记录（新增或更新）";
        if (skippedCount > 0) {
            message += "，跳过 " + skippedCount + " 条精力分配为0的记录";
        }

        return success(message);
    }
    
    /**
     * 批量更新项目参与度分配
     */
    @PreAuthorize("@ss.hasPermi('system:participation:edit')")
    @Log(title = "批量更新项目参与度", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdate")
    public AjaxResult batchUpdate(@RequestBody List<ProjectParticipation> participationList)
    {
        // 如果列表为空，直接返回
        if (participationList == null || participationList.isEmpty()) {
            return AjaxResult.error("提交的数据为空");
        }
        
        // 检查是否在允许的填报周期内
        // 只需要检查第一条记录的月份，假设所有记录都是同一个月份
        checkOperationAllowed(participationList.get(0).getMonth());
        
        try {
            int successCount = 0;
            for (ProjectParticipation participation : participationList) {
                if (participation.getId() != null) {
                    // 将精力分配的月份+1，解决填报周期不匹配问题
                    String adjustedMonth = adjustMonthForEvaluation(participation.getMonth());
                    participation.setMonth(adjustedMonth);

                    successCount += projectParticipationService.updateProjectParticipation(participation);
                }
            }
            return success("成功更新 " + successCount + " 条记录");
        } catch (Exception e) {
            // 记录错误并返回错误信息
            return AjaxResult.error("批量更新失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户当月精力分配数据
     */
    @GetMapping("/userEffort")
    public AjaxResult getUserMonthlyEffort(String userName, String month)
    {
        // 如果没有提供月份，使用当前月份
        if (month == null || month.isEmpty()) {
            // 获取当前年月，格式为yyyy-MM
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            int monthOfYear = cal.get(Calendar.MONTH) + 1;
            month = String.format("%d-%02d", year, monthOfYear);
        }
        
        // 构建查询条件
        ProjectParticipation query = new ProjectParticipation();
        query.setUserName(userName);
        query.setMonth(month);
        
        // 查询用户当月的精力分配数据
        List<ProjectParticipation> list = projectParticipationService.selectProjectParticipationList(query);
        
        return success(list);
    }

    /**
     * 根据用户名、项目ID和月份查询项目参与度分配记录
     */
    @GetMapping("/findByParams")
    public AjaxResult findByParams(
        @RequestParam(value = "userName", required = true) String userName,
        @RequestParam(value = "projectId", required = true) Long projectId,
        @RequestParam(value = "month", required = true) String month)
    {
        ProjectParticipation query = new ProjectParticipation();
        query.setUserName(userName);
        query.setProjectId(projectId);
        query.setMonth(month);
        
        List<ProjectParticipation> list = projectParticipationService.selectProjectParticipationList(query);
        return success(list.size() > 0 ? list.get(0) : null);
    }

    /**
     * 根据用户名、项目ID和月份删除项目参与度分配记录
     */
    @PreAuthorize("@ss.hasPermi('system:participation:remove')")
    @Log(title = "项目参与度分配", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteByParams")
    public AjaxResult deleteByParams(
        @RequestParam(value = "userName", required = true) String userName,
        @RequestParam(value = "projectId", required = true) Long projectId,
        @RequestParam(value = "month", required = true) String month)
    {
        ProjectParticipation query = new ProjectParticipation();
        query.setUserName(userName);
        query.setProjectId(projectId);
        query.setMonth(month);
        
        List<ProjectParticipation> list = projectParticipationService.selectProjectParticipationList(query);
        if (list.size() > 0) {
            return toAjax(projectParticipationService.deleteProjectParticipationById(list.get(0).getId()));
        } else {
            return AjaxResult.error("未找到匹配的记录");
        }
    }

    /**
     * 检查当前是否在允许的填报周期内
     *
     * @param month 评价月份
     * @throws ServiceException 如果不在填报周期内则抛出异常
     */
    private void checkOperationAllowed(String month) {
        // 使用通用配置检查是否允许填报
        if (!evaluationConfigService.checkOperationAllowed("participation", "common")) {
            throw new ServiceException("当前不在精力分配填报周期内，禁止填报");
        }
    }

    /**
     * 调整精力分配月份，解决填报周期不匹配问题
     * 将填报月份+1，使得评分时能查询到对应的精力分配数据
     *
     * @param originalMonth 原始填报月份 (格式: yyyy-MM)
     * @return 调整后的月份 (格式: yyyy-MM)
     */
    private String adjustMonthForEvaluation(String originalMonth) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            Calendar cal = Calendar.getInstance();
            cal.setTime(sdf.parse(originalMonth));
            cal.add(Calendar.MONTH, 1);  // 月份+1
            String adjustedMonth = sdf.format(cal.getTime());

            // 记录调整信息
            logger.info("精力分配月份调整: {} -> {}", originalMonth, adjustedMonth);
            return adjustedMonth;
        } catch (Exception e) {
            logger.error("月份调整失败，使用原月份: {}", originalMonth, e);
            return originalMonth;
        }
    }
}
