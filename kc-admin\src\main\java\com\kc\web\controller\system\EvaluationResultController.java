package com.kc.web.controller.system;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.EvaluationResult;
import com.kc.system.service.IEvaluationResultService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.common.core.domain.entity.SysRole;
import com.kc.common.core.domain.entity.SysDept;
import com.kc.common.utils.SecurityUtils;
import com.kc.system.service.ISysDeptService;
import com.kc.system.domain.dto.EvaluationDetailDTO;
import com.kc.system.service.impl.EvaluationResultServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.kc.common.annotation.DataScope;
import java.util.stream.Collectors;

/**
 * 评价结果Controller
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
@RestController
@RequestMapping("/system/evaluation_result")
public class EvaluationResultController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(EvaluationResultController.class);

    @Autowired
    private IEvaluationResultService evaluationResultService;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询评价结果列表
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluationResult evaluationResult)
    {
        startPage();
        List<EvaluationResult> list = evaluationResultService.selectEvaluationResultList(evaluationResult);
        return getDataTable(list);
    }

    /**
     * 导出评价结果列表
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:export')")
    @Log(title = "评价结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvaluationResult evaluationResult, 
            @RequestParam(value = "exportCurrentPage", required = false, defaultValue = "false") boolean exportCurrentPage,
            @RequestParam(value = "pageNum", required = false) Integer pageNum,
            @RequestParam(value = "pageSize", required = false) Integer pageSize)
    {
        List<EvaluationResult> list;
        
        if (exportCurrentPage && pageNum != null && pageSize != null) {
            // 导出当前页数据
            com.github.pagehelper.PageHelper.startPage(pageNum, pageSize);
            list = evaluationResultService.selectEvaluationResultList(evaluationResult);
            // 不需要从PageInfo中获取，分页后的list就是当前页的数据
        } else {
            // 导出全部数据
            list = evaluationResultService.selectEvaluationResultList(evaluationResult);
        }
        
        ExcelUtil<EvaluationResult> util = new ExcelUtil<EvaluationResult>(EvaluationResult.class);
        util.exportExcel(response, list, "评价结果数据");
    }

    /**
     * 获取评价结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(evaluationResultService.selectEvaluationResultById(id));
    }

    /**
     * 新增评价结果
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:add')")
    @Log(title = "评价结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EvaluationResult evaluationResult)
    {
        return toAjax(evaluationResultService.insertEvaluationResult(evaluationResult));
    }

    /**
     * 修改评价结果
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:edit')")
    @Log(title = "评价结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EvaluationResult evaluationResult)
    {
        return toAjax(evaluationResultService.updateEvaluationResult(evaluationResult));
    }

    /**
     * 删除评价结果
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:remove')")
    @Log(title = "评价结果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(evaluationResultService.deleteEvaluationResultByIds(ids));
    }
    
    /**
     * 计算评价结果
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:calculate')")
    @Log(title = "计算评价结果", businessType = BusinessType.OTHER)
    @PostMapping("/calculate")
    public AjaxResult calculateResults(@RequestParam(value = "evaluationMonth", required = true) String evaluationMonth)
    {
        // 获取当前登录用户
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        Long userId = currentUser.getUserId();

        // 强制将用户视为管理员，计算所有评分结果
        // 传递空字符串表示忽略部门权限限制，处理所有数据
        String deptIds = ""; // 传递空字符串表示处理所有数据

        // 使用Java实现替代存储过程，支持策略模式计算
        int count = evaluationResultService.calculateEvaluationResultsJava(evaluationMonth, userId, deptIds);
        return success("成功计算 " + count + " 条评价结果");
    }
    
    /**
     * 使用Java代码计算评价结果（替代存储过程）
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:calculate')")
    @Log(title = "计算评价结果(Java)", businessType = BusinessType.OTHER)
    @DataScope(deptAlias = "d")
    @PostMapping("/calculate-java")
    public AjaxResult calculateResultsWithJava(@RequestParam(value = "evaluationMonth", required = true) String evaluationMonth)
    {
        // 获取当前登录用户
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        Long userId = currentUser.getUserId();
        
        // 获取用户数据权限范围内的部门ID列表
        String deptIds = null;
        
        // 如果不是管理员，获取数据权限范围内的部门
        if (!SecurityUtils.isAdmin(userId))
        {
            List<SysDept> deptList = deptService.selectDeptList(new SysDept());
            List<Long> deptIdList = new ArrayList<>();
            
            // 获取当前用户的角色数据权限
            for (SysRole role : currentUser.getRoles())
            {
                String dataScope = role.getDataScope();
                if ("1".equals(dataScope)) // 全部数据权限
                {
                    deptIds = null;
                    break;
                }
                else if ("2".equals(dataScope)) // 自定义数据权限
                {
                    List<Long> roleDeptIds = deptService.selectDeptListByRoleId(role.getRoleId());
                    for (Long deptId : roleDeptIds)
                    {
                        deptIdList.add(deptId);
                        // 递归添加子部门
                        addChildDepts(deptList, deptId, deptIdList);
                    }
                }
                else if ("3".equals(dataScope)) // 本部门数据权限
                {
                    deptIdList.add(currentUser.getDeptId());
                    // 递归添加子部门
                    addChildDepts(deptList, currentUser.getDeptId(), deptIdList);
                }
                else if ("4".equals(dataScope)) // 本部门及以下数据权限
                {
                    deptIdList.add(currentUser.getDeptId());
                }
                else if ("5".equals(dataScope)) // 仅本人数据权限
                {
                    // 本方法计算结果，不适用"仅本人"数据权限
                    // 采用本部门权限作为默认处理
                    deptIdList.add(currentUser.getDeptId());
                }
            }
            
            // 如果deptIds不为null，表示有数据权限限制
            if (deptIds == null && !deptIdList.isEmpty())
            {
                deptIds = deptIdList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
                logger.info("用户数据权限范围内的部门ID列表: {}", deptIds);
            }
        }
        
        // 调用Java实现，传递部门ID列表
        int count = evaluationResultService.calculateEvaluationResultsJava(evaluationMonth, userId, deptIds);
        return success("成功计算 " + count + " 条评价结果");
    }
    
    /**
     * 获取评价结果计算明细
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:query')")
    @GetMapping("/detail")
    public AjaxResult getEvaluationDetail(
        @RequestParam(value = "userId", required = true) Long userId,
        @RequestParam(value = "evaluationMonth", required = true) String evaluationMonth)
    {
        try {
            EvaluationDetailDTO detailDTO = evaluationResultService.getEvaluationDetail(userId, evaluationMonth);
            return success(detailDTO);
        } catch (Exception e) {
            logger.error("获取评价结果计算明细失败", e);
            return error("获取评价结果计算明细失败: " + e.getMessage());
        }
    }
    
    /**
     * 计算指定用户列表的评价结果
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_result:calculate')")
    @Log(title = "计算指定用户评价结果", businessType = BusinessType.OTHER)
    @PostMapping("/calculateUsers")
    public AjaxResult calculateUserEvaluationResults(@RequestBody Map<String, Object> params)
    {
        String evaluationMonth = (String) params.get("evaluationMonth");

        // 安全地处理用户ID列表的类型转换
        List<Long> userIds = new ArrayList<>();
        Object userIdsObj = params.get("userIds");
        if (userIdsObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> userIdList = (List<Object>) userIdsObj;
            for (Object userIdObj : userIdList) {
                if (userIdObj instanceof Number) {
                    userIds.add(((Number) userIdObj).longValue());
                } else if (userIdObj instanceof String) {
                    try {
                        userIds.add(Long.parseLong((String) userIdObj));
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析用户ID: {}", userIdObj);
                    }
                }
            }
        }

        if (evaluationMonth == null || evaluationMonth.trim().isEmpty()) {
            return error("评价月份不能为空");
        }

        if (userIds.isEmpty()) {
            return error("用户ID列表不能为空或无效");
        }

        try {
            int result = evaluationResultService.calculateAndInsertEvaluationResults(userIds, evaluationMonth);
            return success("计算完成，处理了 " + result + " 条记录");
        } catch (Exception e) {
            logger.error("计算指定用户评价结果失败", e);
            return error("计算失败：" + e.getMessage());
        }
    }

    /**
     * 递归添加子部门ID
     * @param allDepts 所有部门列表
     * @param parentId 父部门ID
     * @param deptIds 存储结果的列表
     */
    private void addChildDepts(List<SysDept> allDepts, Long parentId, List<Long> deptIds) {
        for (SysDept dept : allDepts) {
            if (dept.getParentId() != null && dept.getParentId().equals(parentId)) {
                if (!deptIds.contains(dept.getDeptId())) {
                    deptIds.add(dept.getDeptId());
                    logger.info("添加子部门: ID={}, 名称={}, 父ID={}", dept.getDeptId(), dept.getDeptName(), dept.getParentId());
                    // 递归添加此部门的子部门
                    addChildDepts(allDepts, dept.getDeptId(), deptIds);
                }
            }
        }
    }
}
