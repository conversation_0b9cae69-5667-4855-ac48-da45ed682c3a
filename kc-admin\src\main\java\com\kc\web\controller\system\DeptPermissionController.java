package com.kc.web.controller.system;

import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.core.domain.entity.SysDept;
import com.kc.common.enums.BusinessType;
import com.kc.common.utils.SecurityUtils;
import com.kc.system.service.IDeptPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门权限控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dept-permission")
public class DeptPermissionController extends BaseController {
    
    @Autowired
    private IDeptPermissionService deptPermissionService;
    
    /**
     * 获取当前用户有权限管理的部门列表
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:list')")
    @GetMapping("/managed-depts")
    public AjaxResult getManagedDepts() {
        String userName = SecurityUtils.getUsername();
        Long userId = SecurityUtils.getUserId();
        List<SysDept> depts = deptPermissionService.getUserManagedDepts(userId, userName);
        return AjaxResult.success(depts);
    }
    
    /**
     * 获取当前用户直接负责的部门列表
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:list')")
    @GetMapping("/direct-managed-depts")
    public AjaxResult getDirectManagedDepts() {
        String userName = SecurityUtils.getUsername();
        List<SysDept> depts = deptPermissionService.getUserDirectManagedDepts(userName);
        return AjaxResult.success(depts);
    }
    
    /**
     * 获取员工评分场景下有权限的部门
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:list')")
    @GetMapping("/evaluation-depts")
    public AjaxResult getDeptsForEvaluation() {
        String userName = SecurityUtils.getUsername();
        List<SysDept> depts = deptPermissionService.getDeptsForEmployeeEvaluation(userName);
        return AjaxResult.success(depts);
    }
    
    /**
     * 获取奖金分配场景下有权限的部门
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:list')")
    @GetMapping("/bonus-depts")
    public AjaxResult getDeptsForBonus() {
        String userName = SecurityUtils.getUsername();
        List<SysDept> depts = deptPermissionService.getDeptsForBonusAllocation(userName);
        return AjaxResult.success(depts);
    }
    
    /**
     * 检查用户是否有权限管理指定部门
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:list')")
    @GetMapping("/check-permission/{deptId}")
    public AjaxResult checkPermission(@PathVariable Long deptId) {
        String userName = SecurityUtils.getUsername();
        boolean hasPermission = deptPermissionService.hasPermissionToManageDept(userName, deptId);
        return AjaxResult.success(hasPermission);
    }
    
    /**
     * 检查用户是否是指定部门的直接负责人
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:list')")
    @GetMapping("/check-direct-leader/{deptId}")
    public AjaxResult checkDirectLeader(@PathVariable Long deptId) {
        String userName = SecurityUtils.getUsername();
        boolean isDirectLeader = deptPermissionService.isDirectLeaderOfDept(userName, deptId);
        return AjaxResult.success(isDirectLeader);
    }
}
