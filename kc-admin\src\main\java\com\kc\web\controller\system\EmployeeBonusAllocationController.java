package com.kc.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.common.utils.SecurityUtils;
import com.kc.system.domain.EmployeeBonusAllocation;
import com.kc.system.domain.dto.EmployeeBonusAllocationDTO;
import com.kc.system.service.IEmployeeBonusAllocationService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;

/**
 * 员工奖金分配Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@RestController
@RequestMapping("/system/employeeBonus")
public class EmployeeBonusAllocationController extends BaseController
{
    @Autowired
    private IEmployeeBonusAllocationService employeeBonusAllocationService;

    /**
     * 查询员工奖金分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmployeeBonusAllocation employeeBonusAllocation)
    {
        startPage();
        List<EmployeeBonusAllocation> list = employeeBonusAllocationService.selectEmployeeBonusAllocationList(employeeBonusAllocation);
        return getDataTable(list);
    }

    /**
     * 根据部门ID和月份查询员工奖金分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:list')")
    @GetMapping("/listByDeptAndMonth/{deptId}/{month}")
    public AjaxResult listByDeptAndMonth(@PathVariable("deptId") Long deptId, @PathVariable("month") String month)
    {
        List<EmployeeBonusAllocation> list = employeeBonusAllocationService.selectByDeptIdAndMonth(deptId, month);
        return success(list);
    }

    /**
     * 根据月份查询员工奖金分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:list')")
    @GetMapping("/listByMonth/{month}")
    public AjaxResult listByMonth(@PathVariable("month") String month)
    {
        List<EmployeeBonusAllocation> list = employeeBonusAllocationService.selectByMonth(month);
        return success(list);
    }

    /**
     * 获取部门负责人可分配的员工列表
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:list')")
    @GetMapping("/getAvailableEmployees/{deptId}/{month}")
    public AjaxResult getAvailableEmployees(@PathVariable("deptId") Long deptId, @PathVariable("month") String month)
    {
        List<EmployeeBonusAllocation> list = employeeBonusAllocationService.getAvailableEmployeesForAllocation(deptId, month);
        return success(list);
    }

    /**
     * 获取当前用户的奖金分配信息
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:query')")
    @GetMapping("/getMyBonus/{month}")
    public AjaxResult getMyBonus(@PathVariable("month") String month)
    {
        Long userId = SecurityUtils.getUserId();
        EmployeeBonusAllocation allocation = employeeBonusAllocationService.selectByUserIdAndMonth(userId, month);
        return success(allocation);
    }

    /**
     * 导出员工奖金分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:export')")
    @Log(title = "员工奖金分配", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmployeeBonusAllocation employeeBonusAllocation)
    {
        List<EmployeeBonusAllocation> list = employeeBonusAllocationService.selectEmployeeBonusAllocationList(employeeBonusAllocation);
        ExcelUtil<EmployeeBonusAllocation> util = new ExcelUtil<EmployeeBonusAllocation>(EmployeeBonusAllocation.class);
        util.exportExcel(response, list, "员工奖金分配数据");
    }

    /**
     * 获取员工奖金分配详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(employeeBonusAllocationService.selectEmployeeBonusAllocationById(id));
    }

    /**
     * 新增员工奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:add')")
    @Log(title = "员工奖金分配", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmployeeBonusAllocation employeeBonusAllocation)
    {
        return toAjax(employeeBonusAllocationService.insertEmployeeBonusAllocation(employeeBonusAllocation));
    }

    /**
     * 批量分配员工奖金
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:add')")
    @Log(title = "批量分配员工奖金", businessType = BusinessType.INSERT)
    @PostMapping("/batchAllocate")
    public AjaxResult batchAllocate(@RequestBody EmployeeBonusAllocationDTO employeeBonusAllocationDTO)
    {
        try {
            int result = employeeBonusAllocationService.batchAllocateEmployeeBonus(employeeBonusAllocationDTO);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 修改员工奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:edit')")
    @Log(title = "员工奖金分配", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmployeeBonusAllocation employeeBonusAllocation)
    {
        return toAjax(employeeBonusAllocationService.updateEmployeeBonusAllocation(employeeBonusAllocation));
    }

    /**
     * 删除员工奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:remove')")
    @Log(title = "员工奖金分配", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(employeeBonusAllocationService.deleteEmployeeBonusAllocationByIds(ids));
    }

    /**
     * 根据部门奖金分配ID删除员工奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:remove')")
    @Log(title = "重置员工奖金分配", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteByDeptBonusId/{deptBonusId}")
    public AjaxResult deleteByDeptBonusId(@PathVariable("deptBonusId") Long deptBonusId)
    {
        try {
            int result = employeeBonusAllocationService.deleteByDeptBonusId(deptBonusId);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 根据月份删除员工奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:employeeBonus:remove')")
    @Log(title = "删除月份员工奖金分配", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteByMonth/{month}")
    public AjaxResult deleteByMonth(@PathVariable("month") String month)
    {
        try {
            int result = employeeBonusAllocationService.deleteByMonth(month);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
}
