package com.kc.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.EmployeeSalary;
import com.kc.system.service.IEmployeeSalaryService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 员工薪酬Controller
 * 
 * <AUTHOR>
 * @date 2025-02-17
 */
@RestController
@RequestMapping("/system/salary")
public class EmployeeSalaryController extends BaseController
{
    @Autowired
    private IEmployeeSalaryService employeeSalaryService;

    /**
     * 查询员工薪酬列表
     */
    @PreAuthorize("@ss.hasPermi('system:salary:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmployeeSalary employeeSalary)
    {
        startPage();
        List<EmployeeSalary> list = employeeSalaryService.selectEmployeeSalaryList(employeeSalary);
        return getDataTable(list);
    }

    /**
     * 导出员工薪酬列表
     */
    @PreAuthorize("@ss.hasPermi('system:salary:export')")
    @Log(title = "员工薪酬", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmployeeSalary employeeSalary)
    {
        List<EmployeeSalary> list = employeeSalaryService.selectEmployeeSalaryList(employeeSalary);
        ExcelUtil<EmployeeSalary> util = new ExcelUtil<EmployeeSalary>(EmployeeSalary.class);
        util.exportExcel(response, list, "员工薪酬数据");
    }

    /**
     * 获取员工薪酬详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:salary:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(employeeSalaryService.selectEmployeeSalaryById(id));
    }

    /**
     * 新增员工薪酬
     */
    @PreAuthorize("@ss.hasPermi('system:salary:add')")
    @Log(title = "员工薪酬", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmployeeSalary employeeSalary)
    {
        return toAjax(employeeSalaryService.insertEmployeeSalary(employeeSalary));
    }

    /**
     * 修改员工薪酬
     */
    @PreAuthorize("@ss.hasPermi('system:salary:edit')")
    @Log(title = "员工薪酬", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmployeeSalary employeeSalary)
    {
        return toAjax(employeeSalaryService.updateEmployeeSalary(employeeSalary));
    }

    /**
     * 删除员工薪酬
     */
    @PreAuthorize("@ss.hasPermi('system:salary:remove')")
    @Log(title = "员工薪酬", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(employeeSalaryService.deleteEmployeeSalaryByIds(ids));
    }

    /**
     * 导入薪资数据
     */
    @Log(title = "薪资管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:salary:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<EmployeeSalary> util = new ExcelUtil<EmployeeSalary>(EmployeeSalary.class);
        List<EmployeeSalary> salaryList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = employeeSalaryService.importSalary(salaryList, updateSupport, operName);
        return success(message);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<EmployeeSalary> util = new ExcelUtil<EmployeeSalary>(EmployeeSalary.class);
        util.importTemplateExcel(response, "薪资数据");
    }
}
