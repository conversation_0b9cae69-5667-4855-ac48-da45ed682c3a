package com.kc.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.DeptBonusAllocation;
import com.kc.system.domain.dto.DeptBonusAllocationDTO;
import com.kc.system.service.IDeptBonusAllocationService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;

/**
 * 部门奖金分配Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@RestController
@RequestMapping("/system/deptBonus")
public class DeptBonusAllocationController extends BaseController
{
    @Autowired
    private IDeptBonusAllocationService deptBonusAllocationService;

    /**
     * 查询部门奖金分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeptBonusAllocation deptBonusAllocation)
    {
        startPage();
        List<DeptBonusAllocation> list = deptBonusAllocationService.selectDeptBonusAllocationList(deptBonusAllocation);
        return getDataTable(list);
    }

    /**
     * 根据月份查询部门奖金分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:list')")
    @GetMapping("/listByMonth/{month}")
    public AjaxResult listByMonth(@PathVariable("month") String month)
    {
        List<DeptBonusAllocation> list = deptBonusAllocationService.selectByMonth(month);
        return success(list);
    }

    /**
     * 检查指定月份是否已经分配过奖金
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:list')")
    @GetMapping("/checkMonth/{month}")
    public AjaxResult checkMonth(@PathVariable("month") String month)
    {
        boolean isAllocated = deptBonusAllocationService.isMonthAllocated(month);
        return success(isAllocated);
    }

    /**
     * 导出部门奖金分配列表
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:export')")
    @Log(title = "部门奖金分配", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeptBonusAllocation deptBonusAllocation)
    {
        List<DeptBonusAllocation> list = deptBonusAllocationService.selectDeptBonusAllocationList(deptBonusAllocation);
        ExcelUtil<DeptBonusAllocation> util = new ExcelUtil<DeptBonusAllocation>(DeptBonusAllocation.class);
        util.exportExcel(response, list, "部门奖金分配数据");
    }

    /**
     * 获取部门奖金分配详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deptBonusAllocationService.selectDeptBonusAllocationById(id));
    }

    /**
     * 根据部门ID和月份获取部门奖金分配信息
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:query')")
    @GetMapping("/getDeptBonus/{deptId}/{month}")
    public AjaxResult getDeptBonus(@PathVariable("deptId") Long deptId, @PathVariable("month") String month)
    {
        DeptBonusAllocation allocation = deptBonusAllocationService.selectByDeptIdAndMonth(deptId, month);
        return success(allocation);
    }

    /**
     * 新增部门奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:add')")
    @Log(title = "部门奖金分配", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeptBonusAllocation deptBonusAllocation)
    {
        return toAjax(deptBonusAllocationService.insertDeptBonusAllocation(deptBonusAllocation));
    }

    /**
     * 批量分配部门奖金
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:add')")
    @Log(title = "批量分配部门奖金", businessType = BusinessType.INSERT)
    @PostMapping("/batchAllocate")
    public AjaxResult batchAllocate(@RequestBody DeptBonusAllocationDTO deptBonusAllocationDTO)
    {
        try {
            int result = deptBonusAllocationService.batchAllocateDeptBonus(deptBonusAllocationDTO);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 修改部门奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:edit')")
    @Log(title = "部门奖金分配", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeptBonusAllocation deptBonusAllocation)
    {
        return toAjax(deptBonusAllocationService.updateDeptBonusAllocation(deptBonusAllocation));
    }

    /**
     * 删除部门奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:remove')")
    @Log(title = "部门奖金分配", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deptBonusAllocationService.deleteDeptBonusAllocationByIds(ids));
    }

    /**
     * 根据月份删除部门奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:deptBonus:remove')")
    @Log(title = "删除月份奖金分配", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteByMonth/{month}")
    public AjaxResult deleteByMonth(@PathVariable("month") String month)
    {
        try {
            int result = deptBonusAllocationService.deleteByMonth(month);
            return toAjax(result);
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }
}
