package com.kc.web.controller.system;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.DeptHighScoreQuota;
import com.kc.system.service.IDeptHighScoreQuotaService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;

/**
 * 部门高分配额Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/system/deptQuota")
public class DeptHighScoreQuotaController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(DeptHighScoreQuotaController.class);

    @Autowired
    private IDeptHighScoreQuotaService deptHighScoreQuotaService;

    /**
     * 查询部门高分配额列表
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeptHighScoreQuota deptHighScoreQuota)
    {
        startPage();
        List<DeptHighScoreQuota> list = deptHighScoreQuotaService.selectDeptHighScoreQuotaList(deptHighScoreQuota);
        return getDataTable(list);
    }

    /**
     * 导出部门高分配额列表
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:export')")
    @Log(title = "部门高分配额", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeptHighScoreQuota deptHighScoreQuota)
    {
        List<DeptHighScoreQuota> list = deptHighScoreQuotaService.selectDeptHighScoreQuotaList(deptHighScoreQuota);
        ExcelUtil<DeptHighScoreQuota> util = new ExcelUtil<DeptHighScoreQuota>(DeptHighScoreQuota.class);
        util.exportExcel(response, list, "部门高分配额数据");
    }

    /**
     * 获取部门高分配额详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deptHighScoreQuotaService.selectDeptHighScoreQuotaById(id));
    }

    /**
     * 获取部门配额信息
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:query')")
    @GetMapping("/info/{deptId}/{year}")
    public AjaxResult getDeptQuotaInfo(@PathVariable("deptId") Long deptId, @PathVariable("year") String year)
    {
        DeptHighScoreQuota quota = deptHighScoreQuotaService.getDeptQuotaInfo(deptId, year);
        return success(quota);
    }

    /**
     * 获取多个部门的配额信息
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:query')")
    @GetMapping("/multiple")
    public AjaxResult getMultipleDeptQuotaInfo(@RequestParam("deptIds") String deptIdsStr,
                                              @RequestParam("year") String year)
    {
        try {
            if (deptIdsStr == null || deptIdsStr.trim().isEmpty()) {
                return error("部门ID列表不能为空");
            }

            if (year == null || year.trim().isEmpty()) {
                return error("年度参数不能为空");
            }

            // 解析部门ID字符串，支持逗号分隔
            String[] deptIdArray = deptIdsStr.split(",");
            List<DeptHighScoreQuota> quotaList = new ArrayList<>();

            for (String deptIdStr : deptIdArray) {
                try {
                    Long deptId = Long.valueOf(deptIdStr.trim());
                    DeptHighScoreQuota quota = deptHighScoreQuotaService.getDeptQuotaInfo(deptId, year);
                    if (quota != null) {
                        quotaList.add(quota);
                    }
                } catch (NumberFormatException e) {
                    logger.warn("无效的部门ID: {}", deptIdStr);
                    continue;
                } catch (Exception e) {
                    logger.error("获取部门{}配额信息失败: {}", deptIdStr, e.getMessage());
                    continue;
                }
            }
            return success(quotaList);
        } catch (Exception e) {
            logger.error("获取多部门配额信息失败", e);
            return error("获取配额信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查配额是否可用
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:query')")
    @GetMapping("/check")
    public AjaxResult checkQuotaAvailable(@RequestParam("deptId") Long deptId, 
                                         @RequestParam("year") String year, 
                                         @RequestParam("count") int count)
    {
        boolean available = deptHighScoreQuotaService.checkQuotaAvailable(deptId, year, count);
        return success(available);
    }

    /**
     * 新增部门高分配额
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:add')")
    @Log(title = "部门高分配额", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeptHighScoreQuota deptHighScoreQuota)
    {
        return toAjax(deptHighScoreQuotaService.insertDeptHighScoreQuota(deptHighScoreQuota));
    }

    /**
     * 修改部门高分配额
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:edit')")
    @Log(title = "部门高分配额", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeptHighScoreQuota deptHighScoreQuota)
    {
        return toAjax(deptHighScoreQuotaService.updateDeptHighScoreQuota(deptHighScoreQuota));
    }

    /**
     * 删除部门高分配额
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:remove')")
    @Log(title = "部门高分配额", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deptHighScoreQuotaService.deleteDeptHighScoreQuotaByIds(ids));
    }

    /**
     * 使用高分配额
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:manage')")
    @Log(title = "使用高分配额", businessType = BusinessType.UPDATE)
    @PostMapping("/use")
    public AjaxResult useQuota(@RequestBody Map<String, Object> params)
    {
        try {
            Long deptId = Long.valueOf(params.get("deptId").toString());
            Long userId = Long.valueOf(params.get("userId").toString());
            String year = params.get("year").toString();
            String month = params.get("month").toString();
            java.math.BigDecimal score = new java.math.BigDecimal(params.get("score").toString());
            Long evaluatorId = Long.valueOf(params.get("evaluatorId").toString());

            boolean success = deptHighScoreQuotaService.useQuota(deptId, userId, year, month, score, evaluatorId);
            if (success) {
                return success("配额使用成功");
            } else {
                return error("配额不足或使用失败");
            }
        } catch (Exception e) {
            logger.error("使用高分配额失败", e);
            return error("使用配额失败: " + e.getMessage());
        }
    }

    /**
     * 释放高分配额
     */
    @PreAuthorize("@ss.hasPermi('system:deptQuota:manage')")
    @Log(title = "释放高分配额", businessType = BusinessType.UPDATE)
    @PostMapping("/release")
    public AjaxResult releaseQuota(@RequestBody Map<String, Object> params)
    {
        try {
            Long deptId = Long.valueOf(params.get("deptId").toString());
            Long userId = Long.valueOf(params.get("userId").toString());
            String year = params.get("year").toString();
            String month = params.get("month").toString();

            boolean success = deptHighScoreQuotaService.releaseQuota(deptId, userId, year, month);
            if (success) {
                return success("配额释放成功");
            } else {
                return error("配额释放失败");
            }
        } catch (Exception e) {
            logger.error("释放高分配额失败", e);
            return error("释放配额失败: " + e.getMessage());
        }
    }
}
