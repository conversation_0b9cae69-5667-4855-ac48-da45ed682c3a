package com.kc.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;
import com.kc.system.domain.QuotaGroup;
import com.kc.system.domain.QuotaGroupDept;
import com.kc.system.domain.QuotaGroupQuota;
import com.kc.system.service.IQuotaGroupService;

/**
 * 配额组Controller
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@RestController
@RequestMapping("/system/quotaGroup")
public class QuotaGroupController extends BaseController
{
    @Autowired
    private IQuotaGroupService quotaGroupService;

    /**
     * 查询配额组列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:list')")
    @GetMapping("/list")
    public TableDataInfo list(QuotaGroup quotaGroup)
    {
        startPage();
        List<QuotaGroup> list = quotaGroupService.selectQuotaGroupList(quotaGroup);
        return getDataTable(list);
    }

    /**
     * 导出配额组列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:export')")
    @Log(title = "配额组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QuotaGroup quotaGroup)
    {
        List<QuotaGroup> list = quotaGroupService.selectQuotaGroupList(quotaGroup);
        ExcelUtil<QuotaGroup> util = new ExcelUtil<QuotaGroup>(QuotaGroup.class);
        util.exportExcel(response, list, "配额组数据");
    }

    /**
     * 获取配额组详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        QuotaGroup quotaGroup = quotaGroupService.selectQuotaGroupById(id);
        
        // 获取配额组的部门列表
        QuotaGroupDept queryDept = new QuotaGroupDept();
        queryDept.setGroupId(id);
        List<QuotaGroupDept> deptList = quotaGroupService.selectQuotaGroupDeptList(queryDept);
        quotaGroup.setDeptList(deptList);
        
        // 获取配额组的配额列表
        QuotaGroupQuota queryQuota = new QuotaGroupQuota();
        queryQuota.setGroupId(id);
        List<QuotaGroupQuota> quotaList = quotaGroupService.selectQuotaGroupQuotaList(queryQuota);
        quotaGroup.setQuotaList(quotaList);
        
        return success(quotaGroup);
    }

    /**
     * 新增配额组
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:add')")
    @Log(title = "配额组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QuotaGroup quotaGroup)
    {
        return toAjax(quotaGroupService.insertQuotaGroup(quotaGroup));
    }

    /**
     * 修改配额组
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:edit')")
    @Log(title = "配额组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuotaGroup quotaGroup)
    {
        return toAjax(quotaGroupService.updateQuotaGroup(quotaGroup));
    }

    /**
     * 删除配额组
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:remove')")
    @Log(title = "配额组", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(quotaGroupService.deleteQuotaGroupByIds(ids));
    }

    /**
     * 查询配额组部门关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:list')")
    @GetMapping("/dept/list")
    public TableDataInfo deptList(QuotaGroupDept quotaGroupDept)
    {
        startPage();
        List<QuotaGroupDept> list = quotaGroupService.selectQuotaGroupDeptList(quotaGroupDept);
        return getDataTable(list);
    }

    /**
     * 查询配额组配额列表
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:list')")
    @GetMapping("/quota/list")
    public TableDataInfo quotaList(QuotaGroupQuota quotaGroupQuota)
    {
        startPage();
        List<QuotaGroupQuota> list = quotaGroupService.selectQuotaGroupQuotaList(quotaGroupQuota);
        return getDataTable(list);
    }

    /**
     * 新增配额组配额
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:add')")
    @Log(title = "配额组配额", businessType = BusinessType.INSERT)
    @PostMapping("/quota")
    public AjaxResult addQuota(@RequestBody QuotaGroupQuota quotaGroupQuota)
    {
        return toAjax(quotaGroupService.insertQuotaGroupQuota(quotaGroupQuota));
    }

    /**
     * 修改配额组配额
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:edit')")
    @Log(title = "配额组配额", businessType = BusinessType.UPDATE)
    @PutMapping("/quota")
    public AjaxResult editQuota(@RequestBody QuotaGroupQuota quotaGroupQuota)
    {
        return toAjax(quotaGroupService.updateQuotaGroupQuota(quotaGroupQuota));
    }

    /**
     * 根据部门ID查询配额组信息
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:query')")
    @GetMapping("/dept/{deptId}")
    public AjaxResult getByDeptId(@PathVariable("deptId") Long deptId)
    {
        QuotaGroup quotaGroup = quotaGroupService.getQuotaGroupByDeptId(deptId);
        return success(quotaGroup);
    }

    /**
     * 检查部门是否属于配额组
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:query')")
    @GetMapping("/check/{deptId}")
    public AjaxResult checkDeptInGroup(@PathVariable("deptId") Long deptId)
    {
        boolean inGroup = quotaGroupService.isDeptInQuotaGroup(deptId);
        return success(inGroup);
    }

    /**
     * 获取部门的配额组配额信息
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:query')")
    @GetMapping("/quota/{deptId}/{year}")
    public AjaxResult getQuotaByDept(@PathVariable("deptId") Long deptId, @PathVariable("year") String year)
    {
        QuotaGroupQuota quota = quotaGroupService.getQuotaGroupQuotaByDept(deptId, year);
        return success(quota);
    }

    /**
     * 初始化配额组年度配额
     */
    @PreAuthorize("@ss.hasPermi('system:quotaGroup:manage')")
    @Log(title = "初始化配额组配额", businessType = BusinessType.INSERT)
    @PostMapping("/quota/init/{groupId}/{year}")
    public AjaxResult initQuota(@PathVariable("groupId") Long groupId, @PathVariable("year") String year)
    {
        QuotaGroupQuota quota = quotaGroupService.initQuotaGroupQuota(groupId, year);
        return success(quota);
    }
}
