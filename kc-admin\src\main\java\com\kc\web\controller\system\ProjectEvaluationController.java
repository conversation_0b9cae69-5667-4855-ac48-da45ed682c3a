package com.kc.web.controller.system;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.ProjectEvaluation;
import com.kc.system.service.IProjectEvaluationService;
import com.kc.system.service.IEvaluationConfigService;
import com.kc.system.service.ISysConfigService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;
import com.kc.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 项目评价Controller
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
@RestController
@RequestMapping("/system/evaluation")
public class ProjectEvaluationController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ProjectEvaluationController.class);

    @Autowired
    private IProjectEvaluationService projectEvaluationService;

    @Autowired
    private IEvaluationConfigService evaluationConfigService;

    @Autowired
    private ISysConfigService configService;

    /**
     * 查询项目评价列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ProjectEvaluation projectEvaluation)
    {
        startPage();
        List<ProjectEvaluation> list = projectEvaluationService.selectProjectEvaluationList(projectEvaluation);
        return getDataTable(list);
    }

    /**
     * 导出项目评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:export')")
    @Log(title = "项目评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectEvaluation projectEvaluation)
    {
        List<ProjectEvaluation> list = projectEvaluationService.selectProjectEvaluationList(projectEvaluation);
        ExcelUtil<ProjectEvaluation> util = new ExcelUtil<ProjectEvaluation>(ProjectEvaluation.class);
        util.exportExcel(response, list, "项目评价数据");
    }

    /**
     * 获取项目评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(projectEvaluationService.selectProjectEvaluationById(id));
    }

    /**
     * 新增项目评价
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:add')")
    @Log(title = "项目评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProjectEvaluation projectEvaluation)
    {
        // 检查评分功能是否开启
        checkEvaluationEnabled();

        // 检查当前日期是否在允许的填报周期内
        String evaluationType = projectEvaluation.getEvaluationType();
        String configType = getConfigTypeByEvaluationType(evaluationType);

        log.info("新增项目评价 - 评价类型: {}, 配置类型: {}, 评价人ID: {}, 被评价人ID: {}",
                evaluationType, configType, projectEvaluation.getEvaluatorId(), projectEvaluation.getEvaluateeId());

        // 使用通用配置检查是否允许填报
        boolean allowed = evaluationConfigService.checkOperationAllowed(configType, "common");
        log.info("填报周期检查结果: {}", allowed);

        if (!allowed) {
            log.error("填报周期检查失败 - 评价类型: {}, 配置类型: {}, 描述: {}",
                    evaluationType, configType, getEvaluationTypeDesc(evaluationType));
            throw new ServiceException("当前不在" + getEvaluationTypeDesc(evaluationType) + "填报周期内，禁止填报");
        }
        
        return toAjax(projectEvaluationService.insertProjectEvaluation(projectEvaluation));
    }

    /**
     * 修改项目评价
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:edit')")
    @Log(title = "项目评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectEvaluation projectEvaluation)
    {
        // 检查评分功能是否开启
        checkEvaluationEnabled();

        // 检查当前日期是否在允许的填报周期内
        String evaluationType = projectEvaluation.getEvaluationType();
        String configType = getConfigTypeByEvaluationType(evaluationType);

        log.info("修改项目评价 - 评价类型: {}, 配置类型: {}", evaluationType, configType);

        // 使用通用配置检查是否允许填报
        boolean allowed = evaluationConfigService.checkOperationAllowed(configType, "common");
        log.info("填报周期检查结果: {}", allowed);

        if (!allowed) {
            throw new ServiceException("当前不在" + getEvaluationTypeDesc(evaluationType) + "填报周期内，禁止填报");
        }
        
        return toAjax(projectEvaluationService.updateProjectEvaluation(projectEvaluation));
    }

    /**
     * 删除项目评价
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:remove')")
    @Log(title = "项目评价", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(projectEvaluationService.deleteProjectEvaluationByIds(ids));
    }

    /**
     * 获取用户参与的项目评分列表
     */
    @GetMapping("/user/{userName}")
    public AjaxResult getUserProjectEvaluations(
        @PathVariable("userName") String userName,
        @RequestParam(value = "evaluationMonth", required = false) String evaluationMonth)
    {
        List<ProjectEvaluation> list = projectEvaluationService.selectUserProjectEvaluations(userName, evaluationMonth);
        return success(list);
    }
    
    /**
     * 通过用户ID获取项目评分列表
     */
    @GetMapping("/user/project/{userId}")
    public AjaxResult getUserProjectEvaluationsByUserId(
        @PathVariable("userId") Long userId,
        @RequestParam(value = "evaluationMonth", required = false) String evaluationMonth)
    {
        // 根据userId获取用户信息
        List<ProjectEvaluation> list = projectEvaluationService.selectUserProjectEvaluationsByUserId(userId, evaluationMonth);
        return success(list);
    }

    /**
     * 根据评价类型获取对应的配置类型
     *
     * @param evaluationType 评价类型
     * @return 配置类型
     */
    private String getConfigTypeByEvaluationType(String evaluationType) {
        if (evaluationType == null) {
            return "unknown";
        }

        switch (evaluationType) {
            case "project_leader":
                return "leader_score";
            case "manager":
                return "manager_score";
            case "participation":
                return "participation";
            // 添加更多可能的评价类型支持
            case "leader_score":
                return "leader_score";
            case "manager_score":
                return "manager_score";
            case "dept_manager":
                return "manager_score";
            case "sub_dept_manager":
                return "manager_score";
            case "parent_manager":
                return "manager_score";
            default:
                // 对于未知类型，记录日志但不抛出异常
                log.warn("未知的评价类型: {}, 将使用默认配置", evaluationType);
                return evaluationType; // 直接返回原始类型，让配置查询去处理
        }
    }
    
    /**
     * 获取评价类型的描述文本
     *
     * @param evaluationType 评价类型
     * @return 描述文本
     */
    private String getEvaluationTypeDesc(String evaluationType) {
        if (evaluationType == null) {
            return "空类型";
        }

        switch (evaluationType) {
            case "project_leader":
                return "项目负责人评分";
            case "manager":
                return "机构负责人评分";
            case "parent_manager":
                return "父部门负责人评分";
            case "sub_dept_manager":
                return "子部门负责人评分";
            case "dept_manager":
                return "部门负责人评分";
            case "participation":
                return "精力分配";
            case "leader_score":
                return "项目负责人评分";
            case "manager_score":
                return "机构负责人评分";
            default:
                return "未知类型(" + evaluationType + ")";
        }
    }

    /**
     * 验证机构负责人评分前置条件
     * 检查被评分人是否为项目工作人员，如果是，则检查项目负责人是否已评分
     */
    @PostMapping("/validateManagerEvaluation")
    public AjaxResult validateManagerEvaluation(@RequestBody Map<String, Object> params)
    {
        Long evaluateeId = Long.valueOf(params.get("evaluateeId").toString());
        String evaluationMonth = (String) params.get("evaluationMonth");

        try {
            Map<String, Object> validationResult = projectEvaluationService.validateManagerEvaluationPreconditions(evaluateeId, evaluationMonth);
            Boolean isValid = (Boolean) validationResult.get("valid");
            String message = (String) validationResult.get("message");

            if (isValid) {
                return success(message);
            } else {
                return error(message);
            }
        } catch (Exception e) {
            return error("验证失败：" + e.getMessage());
        }
    }



    /**
     * 导出部门评分数据
     */
    @PostMapping("/exportDeptEvaluationData")
    public void exportDeptEvaluationData(HttpServletResponse response, @RequestBody Map<String, Object> params) {
        try {
            Long deptId = Long.valueOf(params.get("deptId").toString());
            String evaluationMonth = params.get("evaluationMonth").toString();
            String deptName = params.get("deptName").toString();

            // 获取用户ID列表（可选参数）
            List<Long> userIds = null;
            if (params.containsKey("userIds") && params.get("userIds") != null) {
                @SuppressWarnings("unchecked")
                List<Object> userIdObjects = (List<Object>) params.get("userIds");
                userIds = userIdObjects.stream()
                    .map(obj -> Long.valueOf(obj.toString()))
                    .collect(java.util.stream.Collectors.toList());
                log.info("导出指定用户数据，用户数量: {}", userIds.size());
            }

            log.info("开始导出部门评分数据 - 部门ID: {}, 部门名称: {}, 评价月份: {}", deptId, deptName, evaluationMonth);
            projectEvaluationService.exportDeptEvaluationData(response, deptId, evaluationMonth, deptName, userIds);
        } catch (Exception e) {
            log.error("导出部门评分数据失败", e);
        }
    }

    /**
     * 检查评分功能是否开启
     */
    private void checkEvaluationEnabled() {
        String evaluationEnabled = configService.selectConfigByKey("evaluation.enabled");
        if (!"true".equals(evaluationEnabled)) {
            log.warn("评分功能已关闭，禁止提交评分");
            throw new ServiceException("评分功能已关闭，禁止提交");
        }
    }
}
