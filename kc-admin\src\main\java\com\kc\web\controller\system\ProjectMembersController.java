package com.kc.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.ProjectMembers;
import com.kc.system.service.IProjectMembersService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;
import com.kc.system.service.ISysUserService;
import com.kc.system.service.ISysDeptService;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.common.core.domain.entity.SysDept;
import com.kc.common.annotation.DataScope;
import com.kc.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.ArrayList;

/**
 * 项目成员管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/members")
public class ProjectMembersController extends BaseController
{
    @Autowired
    private IProjectMembersService projectMembersService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;

    private static final Logger log = LoggerFactory.getLogger(ProjectMembersController.class);

    /**
     * 查询项目成员列表
     */
    @PreAuthorize("@ss.hasPermi('system:members:query')")
    @GetMapping("/list")
    @DataScope(deptAlias = "d", userAlias = "u")
    public TableDataInfo list(ProjectMembers projectMembers)
    {
        startPage();
        List<ProjectMembers> list = projectMembersService.selectProjectMembersList(projectMembers);
        return getDataTable(list);
    }

    /**
     * 导出项目成员关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:members:export')")
    @Log(title = "项目成员关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectMembers projectMembers)
    {
        List<ProjectMembers> list = projectMembersService.selectProjectMembersList(projectMembers);
        ExcelUtil<ProjectMembers> util = new ExcelUtil<ProjectMembers>(ProjectMembers.class);
        util.exportExcel(response, list, "项目成员关联数据");
    }

    /**
     * 获取项目成员关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:members:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(projectMembersService.selectProjectMembersById(id));
    }

    /**
     * 新增项目成员
     */
    @PreAuthorize("@ss.hasPermi('system:members:add')")
    @Log(title = "项目成员管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProjectMembers projectMembers) {
        // 检查是否是批量添加
        if (projectMembers.getUserNames() != null && !projectMembers.getUserNames().isEmpty()) {
            // 批量添加成员
            int successCount = 0;
            StringBuilder errorMsg = new StringBuilder();
            
            for (String userName : projectMembers.getUserNames()) {
                ProjectMembers member = new ProjectMembers();
                member.setProjectId(projectMembers.getProjectId());
                member.setUserName(userName);
                member.setRole(projectMembers.getRole());
                
                try {
                    // 检查是否已经存在
                    ProjectMembers existingMember = projectMembersService.selectProjectMemberByUserName(
                        projectMembers.getProjectId(), 
                        userName
                    );
                    
                    if (existingMember != null) {
                        errorMsg.append(String.format("用户 %s 已经是项目成员; ", userName));
                        continue;
                    }
                    
                    // 添加成员
                    if (projectMembersService.insertProjectMembers(member) > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("添加项目成员失败: " + userName, e);
                    errorMsg.append(String.format("添加用户 %s 失败; ", userName));
                }
            }
            
            // 返回结果
            if (successCount == projectMembers.getUserNames().size()) {
                return AjaxResult.success("成功添加所有成员");
            } else if (successCount > 0) {
                return AjaxResult.success(String.format("成功添加 %d 个成员，失败 %d 个。%s", 
                    successCount, 
                    projectMembers.getUserNames().size() - successCount,
                    errorMsg.toString()));
            } else {
                return AjaxResult.error("添加成员失败: " + errorMsg.toString());
            }
        } else {
            // 单个成员添加的原有逻辑
            return toAjax(projectMembersService.insertProjectMembers(projectMembers));
        }
    }

    /**
     * 修改项目成员关联
     */
    @PreAuthorize("@ss.hasPermi('system:members:edit')")
    @Log(title = "项目成员关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectMembers projectMembers)
    {
        return toAjax(projectMembersService.updateProjectMembers(projectMembers));
    }

    /**
     * 删除项目成员
     */
    @PreAuthorize("@ss.hasPermi('system:members:remove')")
    @Log(title = "项目成员管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(projectMembersService.deleteProjectMembersByIds(ids));
    }

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:members:query')")
    @GetMapping("/userList")
    @DataScope(deptAlias = "d", userAlias = "u")
    public AjaxResult getUserList(SysUser user) {
        if (user.getParams() == null) {
            user.setParams(new HashMap<>());
        }
        
        boolean isAdmin = SecurityUtils.isAdmin(SecurityUtils.getUserId());
        user.getParams().put("isAdmin", isAdmin);
        
        // 添加详细的调试日志
        log.info("Current user: {}", SecurityUtils.getUsername());
        log.info("Is admin: {}", isAdmin);
        log.info("User dept ID: {}", SecurityUtils.getLoginUser().getDeptId());
        log.info("User params before query: {}", user.getParams());
        log.info("Data scope: {}", user.getParams().get("dataScope"));
        
        // 获取用户角色信息
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        if (currentUser.getRoles() != null) {
            currentUser.getRoles().forEach(role -> {
                log.info("Role: {}, DataScope: {}", role.getRoleName(), role.getDataScope());
            });
        }
        
        List<SysUser> list = userService.selectUserList(user);
        return success(list);
    }

    /**
     * 获取部门列表
     */
    @PreAuthorize("@ss.hasPermi('system:members:query')")
    @GetMapping("/deptList")
    @DataScope(deptAlias = "d")
    public AjaxResult getDeptList() {
        // 直接获取研究所级别的部门列表
        SysDept dept = new SysDept();
        dept.setParentId(100L);  // 设置父部门ID为100
        List<SysDept> list = deptService.selectDeptList(dept);
        return success(list);
    }

    /**
     * 获取项目成员详细信息（包含部门和用户信息）
     */
    @PreAuthorize("@ss.hasPermi('system:members:query')")
    @GetMapping(value = "/detail/{id}")
    @DataScope(deptAlias = "d", userAlias = "u")
    public AjaxResult getDetailInfo(@PathVariable("id") Long id) {
        ProjectMembers members = projectMembersService.selectProjectMembersById(id);
        if (members != null) {
            // 获取用户信息
            if (members.getUserName() != null) {
                SysUser user = userService.selectUserByUserName(members.getUserName());
                if (user != null) {
                    // 将用户和部门信息放入返回结果
                    Map<String, Object> memberInfo = new HashMap<>();
                    memberInfo.put("id", members.getId());
                    memberInfo.put("projectId", members.getProjectId());
                    memberInfo.put("userName", members.getUserName());
                    memberInfo.put("role", members.getRole());
                    memberInfo.put("nickName", user.getNickName());
                    memberInfo.put("deptId", user.getDeptId());
                    memberInfo.put("deptName", user.getDept() != null ? user.getDept().getDeptName() : "");
                    memberInfo.put("createdAt", members.getCreatedAt());
                    memberInfo.put("updatedAt", members.getUpdatedAt());
                    return success(memberInfo);
                }
            }
        }
        return success(members);
    }

    /**
     * 获取部门信息
     */
    @PreAuthorize("@ss.hasPermi('system:members:query')")
    @GetMapping("/dept/{deptId}")
    @DataScope(deptAlias = "d", userAlias = "u")
    public AjaxResult getDeptInfo(@PathVariable("deptId") Long deptId) {
        return success(deptService.selectDeptById(deptId));
    }

    /**
     * 获取用户参与的所有项目
     */
    @GetMapping("/userProjects/{userName}")
    public AjaxResult getUserProjects(@PathVariable("userName") String userName) {
        try {
            List<Map<String, Object>> projects = projectMembersService.selectUserProjects(userName);
            return success(projects);
        } catch (Exception e) {
            log.error("获取用户参与项目失败: " + userName, e);
            return error("获取用户参与项目失败: " + e.getMessage());
        }
    }

    /**
     * 获取部门下有项目的用户列表及其项目
     */
    @GetMapping("/deptProjectMembers/{deptIds}")
    public AjaxResult getDeptProjectMembers(@PathVariable("deptIds") String deptIds) {
        try {
            // 解析部门ID列表
            List<Long> deptIdList = new ArrayList<>();
            for (String idStr : deptIds.split(",")) {
                try {
                    deptIdList.add(Long.parseLong(idStr));
                } catch (NumberFormatException e) {
                    log.warn("无效的部门ID: " + idStr);
                }
            }
            
            if (deptIdList.isEmpty()) {
                return error("请提供有效的部门ID");
            }
            
            // 调用服务获取部门下有项目的用户及其项目信息
            List<Map<String, Object>> membersWithProjects = projectMembersService.selectDeptMembersWithProjects(deptIdList);
            return success(membersWithProjects);
        } catch (Exception e) {
            log.error("获取部门成员项目信息失败: " + deptIds, e);
            return error("获取部门成员项目信息失败: " + e.getMessage());
        }
    }
}
