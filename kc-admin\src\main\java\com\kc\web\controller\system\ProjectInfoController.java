package com.kc.web.controller.system;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.kc.common.core.domain.entity.SysUser;
import com.kc.system.service.ISysConfigService;
import com.kc.system.service.IEvaluationConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.system.domain.ProjectInfo;
import com.kc.system.service.IProjectInfoService;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.common.core.page.TableDataInfo;
import com.kc.common.core.domain.model.LoginUser;
import com.kc.common.utils.SecurityUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 项目基础信息Controller
 * 
 * <AUTHOR>
 * @date 2025-02-14
 */
@RestController
@RequestMapping("/system/info")
public class ProjectInfoController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ProjectInfoController.class);

    @Autowired
    private IProjectInfoService projectInfoService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IEvaluationConfigService evaluationConfigService;
    /**
     * 查询项目基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProjectInfo projectInfo)
    {
        startPage();
        List<ProjectInfo> list = projectInfoService.selectProjectInfoList(projectInfo);
        return getDataTable(list);
    }

    /**
     * 导出项目基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:export')")
    @Log(title = "项目基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectInfo projectInfo)
    {
        List<ProjectInfo> list = projectInfoService.selectProjectInfoList(projectInfo);
        ExcelUtil<ProjectInfo> util = new ExcelUtil<ProjectInfo>(ProjectInfo.class);
        util.exportExcel(response, list, "项目基础信息数据");
    }

    /**
     * 获取项目基础信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(projectInfoService.selectProjectInfoById(id));
    }

    /**
     * 新增项目基础信息
     */
    @PostMapping
    @PreAuthorize("@ss.hasPermi('system:info:add')")
    @Log(title = "项目管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody ProjectInfo projectInfo) {
        // 检查项目名称是否已存在
        if (projectInfoService.checkProjectNameExists(projectInfo.getProjectName(), projectInfo.getDeptId())) {
            return error("新增项目'" + projectInfo.getProjectName() + "'失败，项目名称已存在");
        }
        
        try {
            int rows = projectInfoService.insertProjectInfo(projectInfo);
            if (rows > 0) {
                // 返回成功结果，并包含新增项目的ID
                return AjaxResult.success("新增成功", projectInfo);  // projectInfo 中会包含自增的 ID
            }
            return error("新增项目失败");
        } catch (Exception e) {
            log.error("新增项目失败", e);
            return error("新增项目失败：" + e.getMessage());
        }
    }

    /**
     * 修改项目基础信息
     */
    @PreAuthorize("@ss.hasPermi('system:info:edit')")
    @Log(title = "项目基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectInfo projectInfo)
    {
        return toAjax(projectInfoService.updateProjectInfo(projectInfo));
    }

    /**
     * 删除项目基础信息
     */
    @PreAuthorize("@ss.hasPermi('system:info:remove')")
    @Log(title = "项目基础信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(projectInfoService.deleteProjectInfoByIds(ids));
    }

    /**
     * 查询用户所有项目工时
     */
    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/userProjectWorkloads/{userName}/{month}")
    public AjaxResult getUserProjectWorkloads(@PathVariable("userName") String userName, 
                                            @PathVariable("month") String month)
    {
        List<Map<String, Object>> workloads = projectInfoService.selectUserProjectWorkloads(userName, month);
        return success(workloads);
    }

    /**
     * 获取当前用户负责的项目列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/userLeadProjects")
    public TableDataInfo getUserLeadProjects() {
        // 从 SecurityContext 获取当前用户名
        String userName = SecurityUtils.getUsername();
        startPage();
        List<ProjectInfo> list = projectInfoService.selectUserLeadProjectList(userName);
        return getDataTable(list);
    }

    /**
     * 获取当前用户部门下的项目列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/dept/projects")
    public TableDataInfo getDeptProjects() {
        // 从 SecurityContext 获取当前用户信息
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser currentUser = loginUser.getUser();
        
        startPage();
        List<ProjectInfo> list = projectInfoService.selectProjectsByDeptId(currentUser.getDeptId());
        return getDataTable(list);
    }

    /**
     * 导入项目数据
     * 
     * @param file 导入文件
     * @param updateSupport 是否更新已存在数据
     * @return 结果
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('system:info:import')")
    @Log(title = "项目管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, Boolean updateSupport) throws Exception {
        // 添加调试日志
        log.info("开始导入文件: {}, 大小: {}", file.getOriginalFilename(), file.getSize());
        
        ExcelUtil<ProjectInfo> util = new ExcelUtil<ProjectInfo>(ProjectInfo.class);
        List<ProjectInfo> projectList = util.importExcel(file.getInputStream());
        
        // 打印读取到的数据
        log.info("从Excel读取到 {} 条数据", projectList.size());
        for (ProjectInfo project : projectList) {
            log.info("读取到数据: {}", project);
        }
        
        String operName = SecurityUtils.getUsername();
        String message = projectInfoService.importProjectInfo(projectList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    /**
     * 下载项目导入模板
     */
    @PreAuthorize("@ss.hasPermi('system:info:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<ProjectInfo> util = new ExcelUtil<ProjectInfo>(ProjectInfo.class);
        List<ProjectInfo> list = new ArrayList<ProjectInfo>();
        list.add(new ProjectInfo()); // 添加一个空行作为模板
        
        // 添加部门ID对照说明
        String[] comments = {
            "备注：承担部门ID对照表",
            "数字能源智联技术研究所：101",
            "友好并网与新型配网技术研究所：102",
            "科技创新部：200",
            "氢能制备及耦合技术研究所：201",
            "风能利用先进技术研究所：202",
            "光能转化先进技术研究所：203",
            "新型储能技术研究所：204"
        };
        
        try {
            util.exportExcel(response, list, "项目数据", comments);
        } catch (Exception e) {
            log.error("下载模板失败", e);
        }
    }

    /**
     * 查询承揽项目信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/contracted")
    public TableDataInfo listContracted(ProjectInfo projectInfo)
    {
        startPage();
        // 设置备注为"承揽项目"的查询条件
        projectInfo.setRemarks("承揽项目");
        List<ProjectInfo> list = projectInfoService.selectProjectInfoList(projectInfo);
        return getDataTable(list);
    }

    /**
     * 检查项目是否有依赖关系（精力分配和评分记录）
     */
    @PreAuthorize("@ss.hasPermi('system:info:query')")
    @GetMapping("/checkDependencies/{projectId}")
    public AjaxResult checkProjectDependencies(@PathVariable("projectId") Long projectId)
    {
        Map<String, Object> dependencies = projectInfoService.checkProjectDependencies(projectId);
        return success(dependencies);
    }

    /**
     * 获取用户可选择的项目列表（排除承揽项目）
     */
    @PreAuthorize("@ss.hasPermi('system:info:list')")
    @GetMapping("/availableProjects")
    public AjaxResult getAvailableProjects()
    {
        List<ProjectInfo> list = projectInfoService.selectAvailableProjects();
        return success(list);
    }

    private void checkWorkloadPermission() {
        // 使用精力分配的填报周期验证，而不是工时填报周期验证
        if (!evaluationConfigService.checkOperationAllowed("participation", "common")) {
            throw new RuntimeException("当前不在精力分配填报周期内，禁止操作工时记录");
        }
    }
    /**
     * 一键填报所有项目成员工时
     */
    @PreAuthorize("@ss.hasPermi('system:workload:add')")
    @PostMapping("/batchAddWorkload")
    public AjaxResult batchAddWorkload() {
        checkWorkloadPermission();
        try {
            String message = projectInfoService.batchAddWorkload();
            return success(message);
        } catch (Exception e) {
            log.error("批量填报工时失败", e);
            return error("批量填报工时失败：" + e.getMessage());
        }
    }
}
