package com.kc.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.core.page.TableDataInfo;
import com.kc.common.enums.BusinessType;
import com.kc.common.utils.poi.ExcelUtil;
import com.kc.system.domain.EvaluationConfig;
import com.kc.system.service.IEvaluationConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 评价系统配置 信息操作处理
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@RestController
@RequestMapping("/system/evaluation_config")
public class EvaluationConfigController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(EvaluationConfigController.class);

    @Autowired
    private IEvaluationConfigService evaluationConfigService;

    /**
     * 获取评价系统配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_config:list')")
    @GetMapping("/list")
    public TableDataInfo list(EvaluationConfig evaluationConfig)
    {
        startPage();
        List<EvaluationConfig> list = evaluationConfigService.selectEvaluationConfigList(evaluationConfig);
        return getDataTable(list);
    }

    /**
     * 导出评价系统配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_config:export')")
    @Log(title = "评价系统配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EvaluationConfig evaluationConfig)
    {
        List<EvaluationConfig> list = evaluationConfigService.selectEvaluationConfigList(evaluationConfig);
        ExcelUtil<EvaluationConfig> util = new ExcelUtil<EvaluationConfig>(EvaluationConfig.class);
        util.exportExcel(response, list, "评价系统配置数据");
    }

    /**
     * 获取评价系统配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(evaluationConfigService.selectEvaluationConfigById(id));
    }

    /**
     * 根据配置类型和月份获取配置
     */
    @GetMapping(value = "/getByTypeAndMonth/{configType}/{month}")
    public AjaxResult getByTypeAndMonth(@PathVariable("configType") String configType, @PathVariable("month") String month)
    {
        EvaluationConfig config = evaluationConfigService.selectEvaluationConfigByTypeAndMonth(configType, month);
        log.info("获取配置信息：类型={}, 月份={}, 结果={}", configType, month, config);
        return success(config);
    }

    /**
     * 检查操作是否允许
     */
    @GetMapping(value = "/checkOperationAllowed/{configType}/{month}")
    public AjaxResult checkOperationAllowed(@PathVariable("configType") String configType, @PathVariable("month") String month)
    {
        boolean allowed = evaluationConfigService.checkOperationAllowed(configType, month);
        log.info("检查操作是否允许：类型={}, 月份={}, 结果={}", configType, month, allowed);
        return success(allowed);
    }

    /**
     * 新增评价系统配置
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_config:add')")
    @Log(title = "评价系统配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody EvaluationConfig evaluationConfig)
    {
        if (!evaluationConfigService.checkConfigTypeAndMonthUnique(evaluationConfig))
        {
            return error("新增评价系统配置失败，配置类型和月份组合已存在");
        }
        evaluationConfig.setCreateBy(getUsername());
        return toAjax(evaluationConfigService.insertEvaluationConfig(evaluationConfig));
    }

    /**
     * 修改评价系统配置
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_config:edit')")
    @Log(title = "评价系统配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody EvaluationConfig evaluationConfig)
    {
        if (!evaluationConfigService.checkConfigTypeAndMonthUnique(evaluationConfig))
        {
            return error("修改评价系统配置失败，配置类型和月份组合已存在");
        }
        evaluationConfig.setUpdateBy(getUsername());
        return toAjax(evaluationConfigService.updateEvaluationConfig(evaluationConfig));
    }

    /**
     * 删除评价系统配置
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation_config:remove')")
    @Log(title = "评价系统配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(evaluationConfigService.deleteEvaluationConfigByIds(ids));
    }
} 