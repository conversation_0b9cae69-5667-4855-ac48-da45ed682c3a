package com.kc.web.controller.system;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import com.kc.common.core.domain.entity.SysDept;
import com.kc.common.core.domain.entity.SysUser;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.kc.common.annotation.Log;
import com.kc.common.core.controller.BaseController;
import com.kc.common.core.domain.AjaxResult;
import com.kc.common.enums.BusinessType;
import com.kc.common.utils.SecurityUtils;
import com.kc.system.domain.DeptBonusAllocation;
import com.kc.system.domain.EmployeeBonusAllocation;
import com.kc.system.domain.dto.EmployeeBonusAllocationDTO;
import com.kc.system.service.IDeptBonusAllocationService;
import com.kc.system.service.IEmployeeBonusAllocationService;
import com.kc.system.service.ISysUserService;
import com.kc.system.service.ISysDeptService;
import com.kc.system.service.IDeptPermissionService;


/**
 * 机构负责人评价与奖金分配Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@RestController
@RequestMapping("/system/managerEvaluationBonus")
public class ManagerEvaluationBonusController extends BaseController
{
    @Autowired
    private IDeptBonusAllocationService deptBonusAllocationService;

    @Autowired
    private IEmployeeBonusAllocationService employeeBonusAllocationService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IDeptPermissionService deptPermissionService;

    /**
     * 获取部门负责人可进行评价和奖金分配的信息
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:manager')")
    @GetMapping("/getDeptInfo/{month}")
    public AjaxResult getDeptInfo(@PathVariable("month") String month)
    {
        try {
            Long currentUserId = SecurityUtils.getUserId();
            SysUser currentUser = userService.selectUserById(currentUserId);
            
            if (currentUser == null || currentUser.getDeptId() == null) {
                return error("当前用户信息异常");
            }

            Long deptId = currentUser.getDeptId();
            
            // 获取部门信息
            SysDept dept = deptService.selectDeptById(deptId);
            if (dept == null) {
                return error("部门信息不存在");
            }

            // 获取部门奖金分配信息
            DeptBonusAllocation deptBonusAllocation = deptBonusAllocationService.selectByDeptIdAndMonth(deptId, month);
            
            // 获取部门员工列表
            SysUser queryUser = new SysUser();
            queryUser.setDeptId(deptId);
            List<SysUser> deptUsers = userService.selectUserList(queryUser);

            // 获取已分配的员工奖金信息
            List<EmployeeBonusAllocation> existingAllocations = null;
            if (deptBonusAllocation != null) {
                existingAllocations = employeeBonusAllocationService.selectByDeptIdAndMonth(deptId, month);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("deptInfo", dept);
            result.put("deptBonusAllocation", deptBonusAllocation);
            result.put("deptUsers", deptUsers);
            result.put("existingAllocations", existingAllocations);
            result.put("canAllocateBonus", deptBonusAllocation != null && 
                      deptBonusAllocation.getRemainingBonus().compareTo(BigDecimal.ZERO) != 0);

            return success(result);
        } catch (Exception e) {
            return error("获取部门信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取部门员工列表（用于奖金分配）
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:manager')")
    @GetMapping("/getDeptEmployees/{deptId}/{month}")
    public AjaxResult getDeptEmployees(@PathVariable("deptId") Long deptId, @PathVariable("month") String month)
    {
        try {
            // 验证当前用户是否有权限访问该部门
            Long currentUserId = SecurityUtils.getUserId();
            SysUser currentUser = userService.selectUserById(currentUserId);

            if (currentUser == null) {
                return error("用户信息不存在");
            }

            // 检查权限：使用部门权限服务进行统一的权限控制
            boolean hasPermission = deptPermissionService.hasPermissionToManageDept(currentUser.getUserName(), deptId);

            if (!hasPermission) {
                return error("无权限访问该部门信息，当前用户：" + currentUser.getUserName() + "，目标部门ID：" + deptId);
            }

            // 获取部门员工列表
            List<EmployeeBonusAllocation> availableEmployees = 
                employeeBonusAllocationService.getAvailableEmployeesForAllocation(deptId, month);

            // 获取已分配的奖金信息 - 按分配者查询所有相关的奖金分配记录
            List<EmployeeBonusAllocation> existingAllocations =
                employeeBonusAllocationService.selectByAllocatorAndMonth(currentUserId, month);

            // 合并信息：如果已有分配记录，则使用已分配的金额
            Map<Long, EmployeeBonusAllocation> existingMap = new HashMap<>();
            if (existingAllocations != null) {
                for (EmployeeBonusAllocation allocation : existingAllocations) {
                    existingMap.put(allocation.getUserId(), allocation);
                }
            }

            for (EmployeeBonusAllocation employee : availableEmployees) {
                EmployeeBonusAllocation existing = existingMap.get(employee.getUserId());
                if (existing != null) {
                    employee.setBonusAmount(existing.getBonusAmount());
                    employee.setAllocationReason(existing.getAllocationReason());
                }
            }

            return success(availableEmployees);
        } catch (Exception e) {
            return error("获取员工列表失败：" + e.getMessage());
        }
    }

    /**
     * 提交员工奖金分配
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:manager')")
    @Log(title = "机构负责人分配员工奖金", businessType = BusinessType.INSERT)
    @PostMapping("/allocateBonus")
    public AjaxResult allocateBonus(@RequestBody EmployeeBonusAllocationDTO employeeBonusAllocationDTO)
    {
        try {
            // 验证当前用户权限
            Long currentUserId = SecurityUtils.getUserId();
            SysUser currentUser = userService.selectUserById(currentUserId);

            if (currentUser == null) {
                return error("用户信息不存在");
            }

            // 检查权限：使用部门权限服务进行统一的权限控制
            Long deptId = employeeBonusAllocationDTO.getDeptId();
            boolean hasPermission = deptPermissionService.hasPermissionToManageDept(currentUser.getUserName(), deptId);

            if (!hasPermission) {
                return error("无权限分配该部门奖金");
            }

            // 验证部门奖金分配是否存在
            DeptBonusAllocation deptBonusAllocation = 
                deptBonusAllocationService.selectDeptBonusAllocationById(employeeBonusAllocationDTO.getDeptBonusId());
            
            if (deptBonusAllocation == null) {
                return error("部门奖金分配不存在，请联系薪酬考核人员先进行部门奖金分配");
            }

            // 执行奖金分配
            int result = employeeBonusAllocationService.batchAllocateEmployeeBonus(employeeBonusAllocationDTO);
            
            if (result > 0) {
                return success("奖金分配成功");
            } else {
                return error("奖金分配失败");
            }
        } catch (Exception e) {
            return error("奖金分配失败：" + e.getMessage());
        }
    }

    /**
     * 获取部门奖金分配状态
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:manager')")
    @GetMapping("/getBonusStatus/{deptId}/{month}")
    public AjaxResult getBonusStatus(@PathVariable("deptId") Long deptId, @PathVariable("month") String month)
    {
        try {
            // 验证权限
            Long currentUserId = SecurityUtils.getUserId();
            SysUser currentUser = userService.selectUserById(currentUserId);

            if (currentUser == null) {
                return error("用户信息不存在");
            }

            // 检查权限：使用部门权限服务进行统一的权限控制
            boolean hasPermission = deptPermissionService.hasPermissionToManageDept(currentUser.getUserName(), deptId);

            if (!hasPermission) {
                return error("无权限查看该部门奖金状态");
            }

            DeptBonusAllocation deptBonusAllocation = deptBonusAllocationService.selectByDeptIdAndMonth(deptId, month);
            
            Map<String, Object> result = new HashMap<>();
            if (deptBonusAllocation != null) {
                result.put("hasBonusAllocation", true);
                result.put("deptBonusId", deptBonusAllocation.getId());
                result.put("totalBonus", deptBonusAllocation.getTotalBonus());
                result.put("allocatedBonus", deptBonusAllocation.getAllocatedBonus());
                result.put("remainingBonus", deptBonusAllocation.getRemainingBonus());
                result.put("allocationStatus", deptBonusAllocation.getAllocationStatus());
                result.put("performanceRank", deptBonusAllocation.getPerformanceRank());
            } else {
                result.put("hasBonusAllocation", false);
                result.put("message", "该月份尚未进行部门奖金分配");
            }

            return success(result);
        } catch (Exception e) {
            return error("获取奖金状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取员工奖金分配历史
     */
    @PreAuthorize("@ss.hasPermi('system:evaluation:manager')")
    @GetMapping("/getAllocationHistory/{deptId}/{month}")
    public AjaxResult getAllocationHistory(@PathVariable("deptId") Long deptId, @PathVariable("month") String month)
    {
        try {
            // 验证权限
            Long currentUserId = SecurityUtils.getUserId();
            SysUser currentUser = userService.selectUserById(currentUserId);

            if (currentUser == null) {
                return error("用户信息不存在");
            }

            // 检查权限：使用部门权限服务进行统一的权限控制
            boolean hasPermission = deptPermissionService.hasPermissionToManageDept(currentUser.getUserName(), deptId);

            if (!hasPermission) {
                return error("无权限查看该部门奖金分配历史");
            }

            List<EmployeeBonusAllocation> allocations = 
                employeeBonusAllocationService.selectByDeptIdAndMonth(deptId, month);

            return success(allocations);
        } catch (Exception e) {
            return error("获取分配历史失败：" + e.getMessage());
        }
    }
}
